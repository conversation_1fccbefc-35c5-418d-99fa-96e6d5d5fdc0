/**
 * 主应用程序
 * 应用程序入口点，负责初始化和协调各个模块
 */

class FileShareApp {
    constructor() {
        this.isInitialized = false;
        this.modules = {};
        this.currentUser = null;
        this.systemInfo = null;
        this.authInfo = null;

        this.init();
    }
    
    /**
     * 初始化应用程序
     */
    async init() {
        try {
            // 检查登录状态
            if (!(await this.checkAuthStatus())) {
                this.redirectToLogin();
                return;
            }

            // 显示加载动画
            Components.Loading.show('正在初始化系统...');

            // 检查浏览器兼容性
            this.checkBrowserCompatibility();

            // 初始化配置
            await this.initializeConfig();

            // 加载用户偏好设置
            this.loadUserPreferences();

            // 初始化模块
            await this.initializeModules();

            // 加载系统信息
            await this.loadSystemInfo();

            // 绑定全局事件
            this.bindGlobalEvents();

            // 设置配置监听
            this.setupConfigListener();

            // 启动应用
            this.startApplication();

            this.isInitialized = true;
            CONFIG.log('info', 'Application initialized successfully');

        } catch (error) {
            CONFIG.log('error', 'Application initialization failed:', error);
            this.handleInitializationError(error);
        } finally {
            // 隐藏加载动画
            if (typeof Components !== 'undefined' && Components.Loading) {
                Components.Loading.hide();
            }
        }
    }

    /**
     * 检查登录状态
     */
    async checkAuthStatus() {
        try {
            const authData = localStorage.getItem('fileShareAuth');
            if (!authData) {
                CONFIG.log('info', '未找到登录信息');
                return false;
            }

            this.authInfo = JSON.parse(authData);

            // 检查token是否存在
            if (!this.authInfo.token) {
                CONFIG.log('info', '登录token无效');
                return false;
            }

            // 更新API基础URL
            if (this.authInfo.serverUrl) {
                CONFIG.API.BASE_URL = `${this.authInfo.serverUrl}/api`;
            }

            // 使用本地时间验证（不依赖服务器）
            const loginTime = this.authInfo.loginTime || 0;
            const now = Date.now();
            const maxAge = 24 * 60 * 60 * 1000; // 24小时

            if (now - loginTime > maxAge) {
                CONFIG.log('info', '登录已过期');
                this.clearAuthInfo();
                return false;
            }

            // 设置默认用户信息（如果没有的话）
            if (!this.currentUser && this.authInfo.user) {
                this.currentUser = this.authInfo.user;
            }

            CONFIG.log('info', '登录状态验证成功（本地验证）');
            return true;

        } catch (error) {
            CONFIG.log('error', '检查登录状态失败:', error);
            this.clearAuthInfo();
            return false;
        }
    }

    /**
     * 清除登录信息
     */
    clearAuthInfo() {
        localStorage.removeItem('fileShareAuth');
        sessionStorage.removeItem('currentServerUrl');
        this.authInfo = null;
    }

    /**
     * 重定向到登录页面
     */
    redirectToLogin() {
        CONFIG.log('info', '重定向到登录页面');
        window.location.href = 'login.html';
    }



    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility() {
        const requiredFeatures = [
            'fetch',
            'Promise',
            'localStorage',
            'FormData',
            'FileReader'
        ];
        
        const missingFeatures = requiredFeatures.filter(feature => {
            return !(feature in window);
        });
        
        if (missingFeatures.length > 0) {
            throw new Error(`浏览器不支持以下功能: ${missingFeatures.join(', ')}`);
        }
        
        // 检查ES6支持
        try {
            eval('const test = () => {};');
        } catch (error) {
            throw new Error('浏览器不支持ES6语法');
        }
    }
    
    /**
     * 加载用户偏好设置
     */
    loadUserPreferences() {
        const preferences = Utils.storage.get(
            CONFIG.STORAGE_KEYS.USER_PREFERENCES,
            CONFIG.DEFAULT_PREFERENCES
        );
        
        this.applyPreferences(preferences);
    }
    
    /**
     * 应用用户偏好设置
     */
    applyPreferences(preferences) {
        // 应用主题
        if (preferences.theme) {
            this.setTheme(preferences.theme);
        }
        
        // 应用语言
        if (preferences.language) {
            this.setLanguage(preferences.language);
        }
        
        // 其他偏好设置
        this.preferences = preferences;
    }
    
    /**
     * 设置主题
     */
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        
        if (theme === 'auto') {
            // 根据系统偏好自动切换
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            const actualTheme = mediaQuery.matches ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', actualTheme);
            
            // 监听系统主题变化
            mediaQuery.addEventListener('change', (e) => {
                const newTheme = e.matches ? 'dark' : 'light';
                document.documentElement.setAttribute('data-theme', newTheme);
            });
        }
    }
    
    /**
     * 设置语言
     */
    setLanguage(language) {
        document.documentElement.setAttribute('lang', language);
        // TODO: 实现国际化
    }
    
    /**
     * 初始化模块
     */
    async initializeModules() {
        try {
            // 检查必要的类是否存在
            const moduleClasses = {
                FileManager: typeof FileManager !== 'undefined' ? FileManager : null,
                FileUploader: typeof FileUploader !== 'undefined' ? FileUploader : null,
                SearchManager: typeof SearchManager !== 'undefined' ? SearchManager : null,
                NotificationManager: typeof NotificationManager !== 'undefined' ? NotificationManager : null
            };

            // 初始化文件管理器
            if (moduleClasses.FileManager) {
                this.modules.fileManager = new moduleClasses.FileManager();
                window.fileManager = this.modules.fileManager;
                CONFIG.log('info', 'FileManager initialized successfully');
            } else {
                CONFIG.log('warn', 'FileManager class not available');
            }

            // 初始化上传器
            if (moduleClasses.FileUploader) {
                this.modules.fileUploader = new moduleClasses.FileUploader();
                window.fileUploader = this.modules.fileUploader;
                CONFIG.log('info', 'FileUploader initialized successfully');
            } else {
                CONFIG.log('warn', 'FileUploader class not available');
            }

            // 初始化搜索管理器
            if (moduleClasses.SearchManager) {
                this.modules.searchManager = new moduleClasses.SearchManager();
                window.searchManager = this.modules.searchManager;
                CONFIG.log('info', 'SearchManager initialized successfully');
            } else {
                CONFIG.log('warn', 'SearchManager class not available');
            }

            // 初始化通知管理器
            if (moduleClasses.NotificationManager) {
                this.modules.notificationManager = new moduleClasses.NotificationManager();
                window.notificationManager = this.modules.notificationManager;
                CONFIG.log('info', 'NotificationManager initialized successfully');
            } else {
                CONFIG.log('warn', 'NotificationManager class not available');
            }

        } catch (error) {
            CONFIG.log('error', `模块初始化失败: ${error.message}`);
            // 不抛出错误，允许应用继续启动
        }
    }
    
    /**
     * 加载系统信息
     */
    async loadSystemInfo() {
        try {
            // 检查SystemAPI是否可用
            if (typeof SystemAPI === 'undefined') {
                CONFIG.log('warn', 'SystemAPI not available, skipping system info load');
                return;
            }

            this.systemInfo = await SystemAPI.getSystemInfo();
            this.updateSystemDisplay();

            // 强制移除任何可能存在的系统状态面板
            this.forceRemoveSystemStatusPanel();
        } catch (error) {
            CONFIG.log('warn', 'Failed to load system info:', error);
            // 系统信息加载失败不影响应用启动
        }
    }
    
    /**
     * 更新系统显示
     */
    updateSystemDisplay() {
        if (!this.systemInfo) return;

        // 更新存储信息
        this.updateStorageInfo();

        // 系统状态面板已移除，跳过系统状态更新

        // 更新用户信息
        this.updateUserInfo();
    }
    
    /**
     * 更新存储信息
     */
    updateStorageInfo() {
        const storageInfo = this.systemInfo.storage;
        if (!storageInfo) return;
        
        const storageBar = Utils.dom.$('.storage-used');
        const storageText = Utils.dom.$('.storage-text');
        
        if (storageBar && storageInfo.total > 0) {
            const percentage = (storageInfo.used / storageInfo.total) * 100;
            storageBar.style.width = percentage + '%';
        }
        
        if (storageText) {
            const usedText = Utils.formatFileSize(storageInfo.used || 0);
            const totalText = Utils.formatFileSize(storageInfo.total || 0);
            
            storageText.innerHTML = `
                <span>已用 ${usedText}</span>
                <span>共 ${totalText}</span>
            `;
        }
    }
    
    /**
     * 更新系统状态
     */
    updateSystemStatus() {
        // 系统状态面板已移除，保留方法以避免错误
        return;
    }

    /**
     * 强制移除系统状态面板
     */
    forceRemoveSystemStatusPanel() {
        try {
            // 查找并移除可能存在的系统状态面板
            const systemStatusSections = document.querySelectorAll('.sidebar-section');
            systemStatusSections.forEach(section => {
                const heading = section.querySelector('h3');
                if (heading && heading.textContent.includes('系统状态')) {
                    section.remove();
                    CONFIG.log('info', '已移除系统状态面板');
                }
            });

            // 移除特定的系统状态元素
            const elementsToRemove = [
                '#online-users-count',
                '#server-status',
                '#server-uptime',
                '.system-status',
                '.status-item'
            ];

            elementsToRemove.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    // 检查是否在侧边栏中
                    if (element.closest('.sidebar')) {
                        element.remove();
                    }
                });
            });

        } catch (error) {
            CONFIG.log('warn', 'Failed to force remove system status panel:', error);
        }
    }

    /**
     * 更新用户信息
     */
    updateUserInfo() {
        if (!this.currentUser) return;

        // 更新用户名
        const usernameElement = Utils.dom.$('#current-username');
        if (usernameElement) {
            usernameElement.textContent = this.currentUser.username || '未知用户';
        }

        // 更新用户角色
        const roleElement = Utils.dom.$('#current-user-role');
        if (roleElement) {
            const role = this.currentUser.is_admin ? '管理员' : '普通用户';
            roleElement.textContent = role;
        }
    }

    /**
     * 格式化运行时间
     */
    formatUptime(seconds) {
        if (seconds < 60) {
            return `${Math.floor(seconds)}秒`;
        } else if (seconds < 3600) {
            return `${Math.floor(seconds / 60)}分钟`;
        } else if (seconds < 86400) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}小时${minutes}分钟`;
        } else {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            return `${days}天${hours}小时`;
        }
    }
    
    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // 窗口大小变化
        Utils.event.on(window, 'resize', Utils.throttle(() => {
            this.handleWindowResize();
        }, 250));
        
        // 在线/离线状态
        Utils.event.on(window, 'online', () => {
            this.handleOnlineStatusChange(true);
        });
        
        Utils.event.on(window, 'offline', () => {
            this.handleOnlineStatusChange(false);
        });
        
        // 页面可见性变化
        Utils.event.on(document, 'visibilitychange', () => {
            this.handleVisibilityChange();
        });
        
        // 全局键盘快捷键
        Utils.event.on(document, 'keydown', (e) => {
            this.handleGlobalKeyboard(e);
        });
        
        // 全局错误处理
        Utils.event.on(window, 'error', (e) => {
            this.handleGlobalError(e);
        });
        
        Utils.event.on(window, 'unhandledrejection', (e) => {
            this.handleUnhandledRejection(e);
        });
    }
    
    /**
     * 启动应用程序
     */
    startApplication() {
        try {
            // 强制移除系统状态面板
            this.forceRemoveSystemStatusPanel();

            // 隐藏加载动画
            if (typeof Components !== 'undefined' && Components.Loading) {
                Components.Loading.hide();
            }

            // 显示欢迎消息
            this.showWelcomeMessage();

            // 开始定期任务
            this.startPeriodicTasks();

            // 绑定UI事件
            this.bindUIEvents();

            // 检查更新
            this.checkForUpdates();
        } catch (error) {
            CONFIG.log('error', 'Failed to start application:', error);
        }
    }
    
    /**
     * 显示欢迎消息
     */
    showWelcomeMessage() {
        try {
            const hour = new Date().getHours();
            let greeting;

            if (hour < 6) {
                greeting = '夜深了，注意休息';
            } else if (hour < 12) {
                greeting = '早上好';
            } else if (hour < 18) {
                greeting = '下午好';
            } else {
                greeting = '晚上好';
            }

            // 检查Components是否可用
            if (typeof Components !== 'undefined' && Components.Toast) {
                Components.Toast.info(`${greeting}！欢迎使用图片文件系统`, 3000);
            } else {
                CONFIG.log('info', `${greeting}！欢迎使用图片文件系统`);
            }
        } catch (error) {
            CONFIG.log('warn', 'Failed to show welcome message:', error);
        }
    }
    
    /**
     * 开始定期任务
     */
    startPeriodicTasks() {
        // 每5分钟更新系统信息
        setInterval(() => {
            this.loadSystemInfo();
        }, 5 * 60 * 1000);
        
        // 每分钟检查连接状态
        setInterval(() => {
            this.checkConnectionStatus();
        }, 60 * 1000);
    }
    
    /**
     * 检查连接状态
     */
    async checkConnectionStatus() {
        try {
            // 检查SystemAPI是否可用
            if (typeof SystemAPI === 'undefined') {
                return;
            }

            await SystemAPI.getSystemStatus();
            // 连接正常
        } catch (error) {
            // 检查Components是否可用
            if (typeof Components !== 'undefined' && Components.Toast) {
                Components.Toast.warning('与服务器连接不稳定');
            }
        }
    }
    
    /**
     * 绑定UI事件
     */
    bindUIEvents() {
        try {
            // 用户菜单
            const userMenuBtn = Utils.dom.$('#user-menu-btn');
            const userDropdown = Utils.dom.$('#user-dropdown');

        if (userMenuBtn && userDropdown) {
            userMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                userDropdown.classList.toggle('show');
            });

            // 点击其他地方关闭菜单
            document.addEventListener('click', () => {
                userDropdown.classList.remove('show');
            });
        }

        // 退出登录
        const logoutBtn = Utils.dom.$('#user-logout');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLogout();
            });
        }

        // 通知按钮
        const notificationsBtn = Utils.dom.$('#notifications-btn');
        if (notificationsBtn) {
            notificationsBtn.addEventListener('click', () => {
                this.toggleNotificationPanel();
            });
        }

        // 上传按钮
        const uploadBtn = Utils.dom.$('#upload-btn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', () => {
                this.showUploadModal();
            });
        }

        // 侧边栏菜单项
        const menuItems = Utils.dom.$$('.sidebar-menu .menu-item a');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleMenuItemClick(item);
            });
        });

        } catch (error) {
            CONFIG.log('error', 'UI事件绑定失败:', error);
        }
    }

    /**
     * 处理用户登出
     */
    async handleLogout() {
        try {
            Components.Loading.show('正在退出...');

            // 调用登出API
            await UserAPI.logout();

            // 清除本地存储
            this.clearAuthInfo();

            // 跳转到登录页面
            window.location.href = 'login.html';

        } catch (error) {
            CONFIG.log('error', '登出失败:', error);
            Components.Toast.error('登出失败，请重试');
        } finally {
            Components.Loading.hide();
        }
    }

    /**
     * 切换通知面板
     */
    toggleNotificationPanel() {
        const panel = Utils.dom.$('#notification-panel');
        if (panel) {
            panel.classList.toggle('show');
        }
    }

    /**
     * 处理菜单项点击
     */
    handleMenuItemClick(menuItem) {
        try {
            // 移除所有菜单项的active状态
            const allMenuItems = Utils.dom.$$('.sidebar-menu .menu-item');
            allMenuItems.forEach(item => item.classList.remove('active'));

            // 添加当前菜单项的active状态
            menuItem.parentElement.classList.add('active');

            // 获取视图类型
            const viewType = menuItem.dataset.view;

            switch (viewType) {
                case 'home':
                    this.showHomeView();
                    break;
                case 'favorites':
                    this.showFavoritesView();
                    break;
                case 'recent':
                    this.showRecentView();
                    break;
                case 'downloads':
                    this.showDownloadsView();
                    break;
                default:
                    CONFIG.log('warn', `Unknown view type: ${viewType}`);
            }
        } catch (error) {
            CONFIG.log('error', 'Failed to handle menu item click:', error);
        }
    }

    /**
     * 显示首页视图
     */
    showHomeView() {
        if (this.modules.fileManager) {
            this.modules.fileManager.loadFiles();
            this.updateBreadcrumb('首页');
        }
    }

    /**
     * 显示收藏夹视图
     */
    showFavoritesView() {
        if (this.modules.fileManager) {
            this.modules.fileManager.showFavorites();
            this.updateBreadcrumb('收藏夹');
        }
    }

    /**
     * 显示最近访问视图
     */
    showRecentView() {
        // TODO: 实现最近访问功能
        Components.Toast.info('最近访问功能开发中...');
    }

    /**
     * 显示下载记录视图
     */
    showDownloadsView() {
        // TODO: 实现下载记录功能
        Components.Toast.info('下载记录功能开发中...');
    }

    /**
     * 更新面包屑导航
     */
    updateBreadcrumb(title) {
        const breadcrumbNav = Utils.dom.$('.breadcrumb-nav');
        if (breadcrumbNav) {
            breadcrumbNav.innerHTML = `
                <a href="#" class="breadcrumb-item">
                    <i class="fas fa-home"></i>
                    ${title}
                </a>
            `;
        }
    }

    /**
     * 显示上传模态框
     */
    showUploadModal() {
        const modal = Utils.dom.$('#upload-modal');
        if (modal) {
            modal.classList.add('show');
        }
    }

    /**
     * 检查更新
     */
    async checkForUpdates() {
        // TODO: 实现更新检查
    }
    
    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        // 响应式布局调整
        const width = window.innerWidth;
        
        if (width < 768) {
            document.body.classList.add('mobile');
        } else {
            document.body.classList.remove('mobile');
        }
    }
    
    /**
     * 处理在线状态变化
     */
    handleOnlineStatusChange(isOnline) {
        if (isOnline) {
            Components.Toast.success('网络连接已恢复');
            // 重新连接WebSocket等
            if (this.modules.notificationManager) {
                this.modules.notificationManager.connectWebSocket();
            }
        } else {
            Components.Toast.error('网络连接已断开');
        }
    }
    
    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时暂停一些操作
        } else {
            // 页面显示时恢复操作
            this.loadSystemInfo();
        }
    }
    
    /**
     * 处理全局键盘快捷键
     */
    handleGlobalKeyboard(event) {
        // Ctrl+/ 显示快捷键帮助
        if (event.ctrlKey && event.key === '/') {
            event.preventDefault();
            this.showKeyboardShortcuts();
        }
        
        // F5 刷新文件列表
        if (event.key === 'F5') {
            event.preventDefault();
            if (this.modules.fileManager) {
                this.modules.fileManager.refresh();
            }
        }
    }
    
    /**
     * 显示键盘快捷键帮助
     */
    showKeyboardShortcuts() {
        const shortcuts = [
            { key: 'Ctrl + A', desc: '全选文件' },
            { key: 'Delete', desc: '删除选中文件' },
            { key: 'Escape', desc: '取消选择' },
            { key: 'F5', desc: '刷新文件列表' },
            { key: 'Ctrl + /', desc: '显示快捷键帮助' }
        ];
        
        const content = shortcuts.map(s => 
            `<div class="shortcut-item"><kbd>${s.key}</kbd><span>${s.desc}</span></div>`
        ).join('');
        
        // TODO: 显示快捷键帮助模态框
        Components.Toast.info('快捷键帮助功能开发中...');
    }
    
    /**
     * 处理全局错误
     */
    handleGlobalError(event) {
        CONFIG.log('error', 'Global error:', event.error);
        
        if (!this.isInitialized) {
            this.handleInitializationError(event.error);
        }
    }
    
    /**
     * 处理未捕获的Promise拒绝
     */
    handleUnhandledRejection(event) {
        CONFIG.log('error', 'Unhandled promise rejection:', event.reason);
        
        // 阻止默认的错误处理
        event.preventDefault();
    }
    
    /**
     * 处理初始化错误
     */
    handleInitializationError(error) {
        Components.Loading.hide();
        
        const errorMessage = error.message || '系统初始化失败';
        
        document.body.innerHTML = `
            <div class="error-page">
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h1>系统初始化失败</h1>
                    <p>${errorMessage}</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        重新加载
                    </button>
                </div>
            </div>
        `;
    }
    
    /**
     * 初始化配置
     */
    async initializeConfig() {
        try {
            CONFIG.log('info', 'Initializing configuration...');

            // 检查ConfigAPI是否可用
            if (typeof ConfigAPI === 'undefined') {
                CONFIG.log('warn', 'ConfigAPI not available, using default configuration');
                return;
            }

            await ConfigAPI.initializeConfig();
            CONFIG.log('info', 'Configuration initialized successfully');
        } catch (error) {
            CONFIG.log('warn', 'Failed to initialize configuration:', error);
            // 配置初始化失败不影响应用启动，使用默认配置
        }
    }

    /**
     * 设置配置变更监听
     */
    setupConfigListener() {
        try {
            // 检查ConfigAPI是否可用
            if (typeof ConfigAPI === 'undefined') {
                CONFIG.log('warn', 'ConfigAPI not available, skipping config listener setup');
                return;
            }

            // 监听WebSocket配置变更事件
            if (window.socket) {
                window.socket.on('config_updated', (data) => {
                    CONFIG.log('info', 'Configuration updated from server');
                    ConfigAPI.applyConfig(data.config);

                    // 显示配置更新通知
                    if (typeof Components !== 'undefined' && Components.Toast) {
                        Components.Toast.info('系统配置已更新');

                        // 如果需要重启，显示提示
                        if (data.restart_required) {
                            Components.Toast.warning('配置变更需要重启服务器才能生效');
                        }
                    }
                });
            }
        } catch (error) {
            CONFIG.log('warn', 'Failed to setup config listener:', error);
        }
    }

    /**
     * 销毁应用程序
     */
    destroy() {
        // 清理资源
        if (this.modules.notificationManager && this.modules.notificationManager.ws) {
            this.modules.notificationManager.ws.close();
        }

        // 清除定时器
        // TODO: 清除所有定时器

        this.isInitialized = false;
    }
}

// 应用程序启动
document.addEventListener('DOMContentLoaded', () => {
    window.app = new FileShareApp();
});

// 全局可用
window.FileShareApp = FileShareApp;
